-- Question 4: Top 10 high-value service areas by net value
SELECT
    ROW_NUMBER() OVER(ORDER BY SUM(gs.sale_amount) DESC) AS rank,
    c.suburb AS service_area,
    COUNT(DISTINCT gs.customer_id) AS customer_count,
    COUNT(gs.glaze_sale_id) AS total_services,
    SUM(gs.sale_amount) AS total_revenue,
    ROUND(SUM(gs.sale_amount) / COUNT(DISTINCT gs.customer_id), 2) AS revenue_per_customer,
    COUNT(DISTINCT gs.sale_type) AS service_types
FROM glaze_sale gs
INNER JOIN customer c ON gs.customer_id = c.customer_id
WHERE gs.sale_amount > 0
GROUP BY c.suburb
HAVING COUNT(DISTINCT gs.customer_id) >= 3
ORDER BY total_revenue DESC
LIMIT 10;

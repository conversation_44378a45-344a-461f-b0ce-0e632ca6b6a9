import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from openpyxl import Workbook
from openpyxl.chart import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Reference
from openpyxl.chart.axis import DateAxis
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 创建images目录
if not os.path.exists('./images'):
    os.makedirs('./images')

# 1. 自我批准违规数据
self_approval_data = {
    'emp_id': [2310, 1842, 1524, 2685, 508, 147],
    'employee_name': ['<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>'],
    'position_title': ['Salesperson', 'Salesperson', 'Salesperson', 'Salesperson', 'Salesperson', 'Salesperson'],
    'violation_count': [2, 2, 2, 2, 2, 2],
    'total_violation_amount': [630.86, 586.89, 504.06, 429.60, 390.78, 298.66]
}

# 2. 异常高额交易数据
high_value_anomalies_data = {
    'glaze_sale_id': [4038, 4031, 4028, 4034, 4026, 4022, 4023, 4025, 4027, 4041],
    'sale_amount': [22000.00] * 10,
    'date_ordered': ['2023-07-08', '2023-05-25', '2023-04-12', '2023-06-13', '2023-03-12', 
                     '2022-12-10', '2022-12-16', '2023-02-22', '2023-04-10', '2023-08-01'],
    'customer_id': [331, 272, 225, 293, 196, 102, 108, 179, 223, 361],
    'emp_id': [2204, 1141, 1048, 655, 1230, 1795, 2742, 2429, 1230, 2429],
    'z_score': [4.54] * 10
}

# 3. 员工ID模式数据
employee_id_pattern_data = {
    'emp_id': [195, 234, 325, 377, 507, 585, 728, 910, 1404, 1482, 1599, 1638, 1716, 2067, 2288],
    'employee_name': ['Alijah Fleming', 'Kassandra Blackwell', 'Maddox Stewart', 'Caitlin Sawyer', 
                      'Colin Garrett', 'Patrick Gaines', 'Sandra Daniels', 'Wade Crane', 
                      'Kiana Walton', 'Franklin Mathis', 'Christian Cobb', 'Adrian Day', 
                      'Iyana Houston', 'Sullivan Larsen', 'Chelsea Dennis'],
    'divisible_by_13': ['Yes'] * 15
}

# 4. 风险评估汇总数据
risk_summary_data = {
    'risk_category': ['自我批准违规', '异常高额交易', '员工ID模式异常', '系统权限缺陷'],
    'risk_level': ['中等', '极高', '高等', '高等'],
    'potential_loss': [2840.85, 220000.00, 50000.00, 100000.00],
    'probability': [100, 90, 80, 70],
    'expected_loss': [2840.85, 198000.00, 40000.00, 70000.00]
}

# 创建DataFrame
df_self_approval = pd.DataFrame(self_approval_data)
df_anomalies = pd.DataFrame(high_value_anomalies_data)
df_id_pattern = pd.DataFrame(employee_id_pattern_data)
df_risk_summary = pd.DataFrame(risk_summary_data)

# 创建Excel工作簿
wb = Workbook()
ws = wb.active
ws.title = "欺诈风险分析总览"

# 添加工作表
ws_self_approval = wb.create_sheet("自我批准违规")
ws_anomalies = wb.create_sheet("异常高额交易")
ws_id_pattern = wb.create_sheet("员工ID模式")
ws_risk_summary = wb.create_sheet("风险评估汇总")

# 写入数据到工作表
for r in dataframe_to_rows(df_self_approval, index=False, header=True):
    ws_self_approval.append(r)

for r in dataframe_to_rows(df_anomalies, index=False, header=True):
    ws_anomalies.append(r)

for r in dataframe_to_rows(df_id_pattern, index=False, header=True):
    ws_id_pattern.append(r)

for r in dataframe_to_rows(df_risk_summary, index=False, header=True):
    ws_risk_summary.append(r)

# 保存Excel文件
wb.save('GigaGlow_欺诈风险分析.xlsx')
print("Excel文件已创建：GigaGlow_欺诈风险分析.xlsx")

# 创建可视化图表
# 1. 自我批准违规图表
plt.figure(figsize=(12, 8))
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

# 违规金额条形图
ax1.bar(df_self_approval['employee_name'], df_self_approval['total_violation_amount'], 
        color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'])
ax1.set_title('自我批准违规 - 员工违规金额分布', fontsize=14, fontweight='bold')
ax1.set_xlabel('员工姓名', fontsize=12)
ax1.set_ylabel('违规金额 ($)', fontsize=12)
ax1.tick_params(axis='x', rotation=45)
for i, v in enumerate(df_self_approval['total_violation_amount']):
    ax1.text(i, v + 10, f'${v:.2f}', ha='center', va='bottom', fontweight='bold')

# 违规次数饼图
ax2.pie([len(df_self_approval), 142-len(df_self_approval)], 
        labels=['违规员工', '正常员工'], 
        colors=['#FF6B6B', '#95E1D3'],
        autopct='%1.1f%%',
        startangle=90)
ax2.set_title('违规员工占比', fontsize=14, fontweight='bold')

plt.tight_layout()
plt.savefig('./images/self_approval_violations.png', dpi=300, bbox_inches='tight')
plt.close()

print("自我批准违规图表已保存：./images/self_approval_violations.png")

# 2. 异常高额交易图表
plt.figure(figsize=(15, 10))
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

# 时间序列图
df_anomalies['date_ordered'] = pd.to_datetime(df_anomalies['date_ordered'])
ax1.plot(df_anomalies['date_ordered'], df_anomalies['sale_amount'],
         marker='o', linewidth=2, markersize=8, color='#FF6B6B')
ax1.set_title('异常高额交易时间分布', fontsize=14, fontweight='bold')
ax1.set_xlabel('交易日期', fontsize=12)
ax1.set_ylabel('交易金额 ($)', fontsize=12)
ax1.grid(True, alpha=0.3)
ax1.tick_params(axis='x', rotation=45)

# Z-Score分布
ax2.bar(range(len(df_anomalies)), df_anomalies['z_score'],
        color='#FF6B6B', alpha=0.7)
ax2.axhline(y=2.5, color='orange', linestyle='--', label='异常阈值 (Z=2.5)')
ax2.axhline(y=4.0, color='red', linestyle='--', label='极端异常 (Z=4.0)')
ax2.set_title('异常交易Z-Score分布', fontsize=14, fontweight='bold')
ax2.set_xlabel('交易序号', fontsize=12)
ax2.set_ylabel('Z-Score', fontsize=12)
ax2.legend()
ax2.grid(True, alpha=0.3)

# 员工参与度
emp_counts = df_anomalies['emp_id'].value_counts()
ax3.bar(range(len(emp_counts)), emp_counts.values,
        color=['#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'])
ax3.set_title('涉及异常交易的员工分布', fontsize=14, fontweight='bold')
ax3.set_xlabel('员工ID', fontsize=12)
ax3.set_ylabel('异常交易次数', fontsize=12)
ax3.set_xticks(range(len(emp_counts)))
ax3.set_xticklabels([f'ID:{emp_id}' for emp_id in emp_counts.index], rotation=45)

# 交易金额统一性
ax4.hist([22000], bins=1, color='#FF6B6B', alpha=0.7, edgecolor='black')
ax4.set_title('异常交易金额分布', fontsize=14, fontweight='bold')
ax4.set_xlabel('交易金额 ($)', fontsize=12)
ax4.set_ylabel('交易次数', fontsize=12)
ax4.text(22000, 5, f'所有10笔交易\n金额均为$22,000',
         ha='center', va='center', fontsize=12, fontweight='bold',
         bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))

plt.tight_layout()
plt.savefig('./images/high_value_anomalies.png', dpi=300, bbox_inches='tight')
plt.close()

print("异常高额交易图表已保存：./images/high_value_anomalies.png")

# 3. 员工ID模式分析图表
plt.figure(figsize=(14, 10))
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

# ID分布图
ax1.scatter(df_id_pattern['emp_id'], [1]*len(df_id_pattern),
           s=100, color='#FF6B6B', alpha=0.7)
ax1.set_title('可被13整除的员工ID分布', fontsize=14, fontweight='bold')
ax1.set_xlabel('员工ID', fontsize=12)
ax1.set_ylabel('', fontsize=12)
ax1.set_yticks([])
ax1.grid(True, alpha=0.3)

# 统计概率分析
categories = ['可被13整除', '不可被13整除']
values = [15, 127]  # 15个可被13整除，127个不可被13整除
expected = [142/13, 142*12/13]  # 期望值
ax2.bar(categories, values, color=['#FF6B6B', '#4ECDC4'], alpha=0.7, label='实际值')
ax2.bar(categories, expected, color=['#FF6B6B', '#4ECDC4'], alpha=0.3, label='期望值')
ax2.set_title('员工ID模式 - 实际vs期望分布', fontsize=14, fontweight='bold')
ax2.set_ylabel('员工数量', fontsize=12)
ax2.legend()
for i, (v, e) in enumerate(zip(values, expected)):
    ax2.text(i, v + 2, f'实际: {v}', ha='center', va='bottom', fontweight='bold')
    ax2.text(i, e + 2, f'期望: {e:.1f}', ha='center', va='bottom', fontweight='bold', color='gray')

# 概率分析
prob_data = {'类别': ['观察概率', '理论概率'], '概率': [10.6, 7.7]}
ax3.bar(prob_data['类别'], prob_data['概率'],
        color=['#FF6B6B', '#4ECDC4'], alpha=0.7)
ax3.set_title('可被13整除的概率分析', fontsize=14, fontweight='bold')
ax3.set_ylabel('概率 (%)', fontsize=12)
ax3.axhline(y=7.7, color='orange', linestyle='--', label='理论期望 (7.7%)')
ax3.legend()
for i, v in enumerate(prob_data['概率']):
    ax3.text(i, v + 0.2, f'{v}%', ha='center', va='bottom', fontweight='bold')

# 风险等级评估
ax4.pie([15, 127], labels=['高风险ID模式', '正常ID'],
        colors=['#FF6B6B', '#95E1D3'],
        autopct='%1.1f%%',
        startangle=90)
ax4.set_title('员工ID风险分布', fontsize=14, fontweight='bold')

plt.tight_layout()
plt.savefig('./images/employee_id_pattern.png', dpi=300, bbox_inches='tight')
plt.close()

print("员工ID模式图表已保存：./images/employee_id_pattern.png")

# 4. 欺诈风险热力图
plt.figure(figsize=(12, 8))

# 创建风险矩阵数据
risk_matrix = np.array([
    [2840.85, 0, 0, 0],      # 低影响
    [0, 0, 50000, 100000],   # 中影响
    [0, 220000, 0, 0],       # 高影响
])

risk_labels = ['自我批准违规', '异常高额交易', '员工ID模式', '系统权限缺陷']
impact_labels = ['低影响\n(<$10K)', '中影响\n($10K-$100K)', '高影响\n(>$100K)']
probability_labels = ['低概率\n(<50%)', '中概率\n(50%-80%)', '高概率\n(>80%)']

# 创建热力图
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

# 风险金额热力图
im1 = ax1.imshow(risk_matrix, cmap='Reds', aspect='auto')
ax1.set_title('欺诈风险热力图 - 潜在损失金额', fontsize=14, fontweight='bold')
ax1.set_xlabel('风险类别', fontsize=12)
ax1.set_ylabel('影响等级', fontsize=12)
ax1.set_xticks(range(len(risk_labels)))
ax1.set_xticklabels(risk_labels, rotation=45, ha='right')
ax1.set_yticks(range(len(impact_labels)))
ax1.set_yticklabels(impact_labels)

# 添加数值标签
for i in range(len(impact_labels)):
    for j in range(len(risk_labels)):
        if risk_matrix[i, j] > 0:
            text = ax1.text(j, i, f'${risk_matrix[i, j]:,.0f}',
                           ha="center", va="center", color="white", fontweight='bold')

# 风险概率矩阵
prob_matrix = np.array([
    [100, 0, 0, 0],    # 高概率
    [0, 0, 80, 70],    # 中概率
    [0, 90, 0, 0],     # 低概率 (异常交易虽然发生了，但持续概率相对较低)
])

im2 = ax2.imshow(prob_matrix, cmap='YlOrRd', aspect='auto')
ax2.set_title('欺诈风险热力图 - 发生概率', fontsize=14, fontweight='bold')
ax2.set_xlabel('风险类别', fontsize=12)
ax2.set_ylabel('概率等级', fontsize=12)
ax2.set_xticks(range(len(risk_labels)))
ax2.set_xticklabels(risk_labels, rotation=45, ha='right')
ax2.set_yticks(range(len(probability_labels)))
ax2.set_yticklabels(probability_labels)

# 添加概率标签
for i in range(len(probability_labels)):
    for j in range(len(risk_labels)):
        if prob_matrix[i, j] > 0:
            text = ax2.text(j, i, f'{prob_matrix[i, j]}%',
                           ha="center", va="center", color="white", fontweight='bold')

plt.tight_layout()
plt.savefig('./images/fraud_risk_heatmap.png', dpi=300, bbox_inches='tight')
plt.close()

print("欺诈风险热力图已保存：./images/fraud_risk_heatmap.png")

# 5. 综合风险评估总结图表
plt.figure(figsize=(16, 10))
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

# 风险等级分布
risk_levels = df_risk_summary['risk_level'].value_counts()
colors = {'极高': '#FF4444', '高等': '#FF8800', '中等': '#FFAA00', '低等': '#44AA44'}
ax1.pie(risk_levels.values, labels=risk_levels.index,
        colors=[colors[level] for level in risk_levels.index],
        autopct='%1.1f%%', startangle=90)
ax1.set_title('欺诈风险等级分布', fontsize=14, fontweight='bold')

# 潜在损失对比
ax2.bar(df_risk_summary['risk_category'], df_risk_summary['potential_loss'],
        color=['#FFAA00', '#FF4444', '#FF8800', '#FF8800'])
ax2.set_title('各类欺诈风险潜在损失对比', fontsize=14, fontweight='bold')
ax2.set_xlabel('风险类别', fontsize=12)
ax2.set_ylabel('潜在损失 ($)', fontsize=12)
ax2.tick_params(axis='x', rotation=45)
for i, v in enumerate(df_risk_summary['potential_loss']):
    ax2.text(i, v + 5000, f'${v:,.0f}', ha='center', va='bottom', fontweight='bold')

# 期望损失分析
ax3.bar(df_risk_summary['risk_category'], df_risk_summary['expected_loss'],
        color=['#FFAA00', '#FF4444', '#FF8800', '#FF8800'], alpha=0.7)
ax3.set_title('期望损失分析 (潜在损失 × 概率)', fontsize=14, fontweight='bold')
ax3.set_xlabel('风险类别', fontsize=12)
ax3.set_ylabel('期望损失 ($)', fontsize=12)
ax3.tick_params(axis='x', rotation=45)
for i, v in enumerate(df_risk_summary['expected_loss']):
    ax3.text(i, v + 3000, f'${v:,.0f}', ha='center', va='bottom', fontweight='bold')

# 风险优先级矩阵
ax4.scatter(df_risk_summary['probability'], df_risk_summary['potential_loss'],
           s=200, c=['#FFAA00', '#FF4444', '#FF8800', '#FF8800'], alpha=0.7)
ax4.set_title('风险优先级矩阵', fontsize=14, fontweight='bold')
ax4.set_xlabel('发生概率 (%)', fontsize=12)
ax4.set_ylabel('潜在损失 ($)', fontsize=12)
ax4.grid(True, alpha=0.3)

# 添加风险类别标签
for i, txt in enumerate(df_risk_summary['risk_category']):
    ax4.annotate(txt, (df_risk_summary['probability'][i], df_risk_summary['potential_loss'][i]),
                xytext=(5, 5), textcoords='offset points', fontsize=10, fontweight='bold')

plt.tight_layout()
plt.savefig('./images/fraud_risk_summary.png', dpi=300, bbox_inches='tight')
plt.close()

print("综合风险评估图表已保存：./images/fraud_risk_summary.png")

print("\n所有欺诈风险分析图表已生成完成！")
print("生成的文件：")
print("- Excel文件：GigaGlow_欺诈风险分析.xlsx")
print("- 图表文件：")
print("  * ./images/self_approval_violations.png")
print("  * ./images/high_value_anomalies.png")
print("  * ./images/employee_id_pattern.png")
print("  * ./images/fraud_risk_heatmap.png")
print("  * ./images/fraud_risk_summary.png")

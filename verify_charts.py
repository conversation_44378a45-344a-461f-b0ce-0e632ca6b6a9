import os
from PIL import Image
import matplotlib.pyplot as plt

def verify_chart_files():
    """Verify that all chart files exist and have correct properties"""
    
    chart_files = [
        './images/self_approval_violations.png',
        './images/high_value_anomalies.png',
        './images/employee_id_pattern.png',
        './images/fraud_risk_summary.png'
    ]
    
    print("Chart File Verification Report")
    print("=" * 50)
    
    for chart_file in chart_files:
        if os.path.exists(chart_file):
            try:
                # Open and check image properties
                img = Image.open(chart_file)
                width, height = img.size
                
                print(f"✓ {chart_file}")
                print(f"  - Resolution: {width}x{height} pixels")
                print(f"  - Format: {img.format}")
                print(f"  - Mode: {img.mode}")
                print(f"  - File size: {os.path.getsize(chart_file):,} bytes")
                print()
                
            except Exception as e:
                print(f"✗ {chart_file} - Error: {e}")
        else:
            print(f"✗ {chart_file} - File not found")
    
    # Check Excel file
    excel_file = 'GigaGlow_Fraud_Risk_Analysis_English.xlsx'
    if os.path.exists(excel_file):
        print(f"✓ {excel_file}")
        print(f"  - File size: {os.path.getsize(excel_file):,} bytes")
    else:
        print(f"✗ {excel_file} - File not found")
    
    print("\nVerification completed!")

if __name__ == "__main__":
    verify_chart_files()

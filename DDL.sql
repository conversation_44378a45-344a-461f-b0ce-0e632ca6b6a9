CREATE TABLE "public"."authorizations" (
  "authorizations_id" "pg_catalog"."int4" NOT NULL DEFAULT nextval('authorizations_authorizations_id_seq1'::regclass),
  "emp_id" "pg_catalog"."int4",
  "table_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "approve_rights" "pg_catalog"."bool",
  CONSTRAINT "authorizations_pkey" PRIMARY KEY ("authorizations_id"),
  CONSTRAINT "fk_authorizations_employee" FOREIGN KEY ("emp_id") REFERENCES "public"."employee" ("emp_id") ON DELETE NO ACTION ON UPDATE NO ACTION
)
;

ALTER TABLE "public"."authorizations" 
  OWNER TO "postgres";
CREATE TABLE "public"."backup_log" (
  "backup_log_id" "pg_catalog"."int4" NOT NULL DEFAULT nextval('backup_log_backup_log_id_seq1'::regclass),
  "backup_date" "pg_catalog"."timestamp",
  "status_code" "pg_catalog"."text" COLLATE "pg_catalog"."default",
  CONSTRAINT "backup_log_pkey" PRIMARY KEY ("backup_log_id")
)
;

ALTER TABLE "public"."backup_log" 
  OWNER TO "postgres";
CREATE TABLE "public"."casual_hourlyrates" (
  "job_position_id" "pg_catalog"."int4" NOT NULL,
  "start_date" "pg_catalog"."date" NOT NULL,
  "casual_hourly_rate" "pg_catalog"."numeric",
  CONSTRAINT "casual_hourlyrates_pkey" PRIMARY KEY ("job_position_id", "start_date"),
  CONSTRAINT "fk_casual_hourlyrates_job_position" FOREIGN KEY ("job_position_id") REFERENCES "public"."job_position" ("job_position_id") ON DELETE NO ACTION ON UPDATE NO ACTION
)
;

ALTER TABLE "public"."casual_hourlyrates" 
  OWNER TO "postgres";
CREATE TABLE "public"."customer" (
  "customer_id" "pg_catalog"."int4" NOT NULL DEFAULT nextval('customer_customer_id_seq'::regclass),
  "customer_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "street" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "suburb" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "city" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "state" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "post_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "amount_owed" "pg_catalog"."numeric",
  "notes" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "credit_limit" "pg_catalog"."numeric",
  "customer_emp_id" "pg_catalog"."int4",
  CONSTRAINT "customer_pkey" PRIMARY KEY ("customer_id"),
  CONSTRAINT "fk_customer_employee" FOREIGN KEY ("customer_emp_id") REFERENCES "public"."employee" ("emp_id") ON DELETE NO ACTION ON UPDATE NO ACTION
)
;

ALTER TABLE "public"."customer" 
  OWNER TO "postgres";
CREATE TABLE "public"."employee" (
  "emp_id" "pg_catalog"."int4" NOT NULL DEFAULT nextval('employee_emp_id_seq'::regclass),
  "first_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "last_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "address1" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "address2" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "city" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "state" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "post_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "phone" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "start_date" "pg_catalog"."date",
  "end_date" "pg_catalog"."date",
  "job_position_id" "pg_catalog"."int4",
  "status_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "current_standard_hours" "pg_catalog"."int4",
  "next_of_kin" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  CONSTRAINT "employee_pkey" PRIMARY KEY ("emp_id"),
  CONSTRAINT "fk_employee_job_position" FOREIGN KEY ("job_position_id") REFERENCES "public"."job_position" ("job_position_id") ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT "fk_employee_status_lookup" FOREIGN KEY ("status_code") REFERENCES "public"."status_lookup" ("status_code") ON DELETE NO ACTION ON UPDATE NO ACTION
)
;

ALTER TABLE "public"."employee" 
  OWNER TO "postgres";
CREATE TABLE "public"."glaze_sale" (
  "glaze_sale_id" "pg_catalog"."int4" NOT NULL DEFAULT nextval('glaze_sale_glaze_sale_id_seq'::regclass),
  "customer_id" "pg_catalog"."int8",
  "date_ordered" "pg_catalog"."timestamp",
  "vendor_id" "pg_catalog"."int4",
  "emp_id" "pg_catalog"."int4",
  "referrer_emp_id" "pg_catalog"."int4",
  "house_area" "pg_catalog"."int4",
  "sale_amount" "pg_catalog"."numeric",
  "sale_type" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "satisfaction_score" "pg_catalog"."int4",
  CONSTRAINT "glaze_sale_pkey" PRIMARY KEY ("glaze_sale_id"),
  CONSTRAINT "fk_glaze_sale_custoemr_id" FOREIGN KEY ("customer_id") REFERENCES "public"."customer" ("customer_id") ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT "fk_glaze_sale_emp_id" FOREIGN KEY ("emp_id") REFERENCES "public"."employee" ("emp_id") ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT "fk_glaze_sale_referrer_emp_id" FOREIGN KEY ("referrer_emp_id") REFERENCES "public"."employee" ("emp_id") ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT "fk_glaze_sale_vendor_id" FOREIGN KEY ("vendor_id") REFERENCES "public"."vendor" ("vendor_id") ON DELETE NO ACTION ON UPDATE NO ACTION
)
;

ALTER TABLE "public"."glaze_sale" 
  OWNER TO "postgres";
CREATE TABLE "public"."job_position" (
  "job_position_id" "pg_catalog"."int4" NOT NULL,
  "position_title" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  CONSTRAINT "job_position_pkey1" PRIMARY KEY ("job_position_id")
)
;

ALTER TABLE "public"."job_position" 
  OWNER TO "postgres";
CREATE TABLE "public"."month_cleaner_satisfaction" (
  "month_cleaner_satisfaction_id" "pg_catalog"."int4" NOT NULL DEFAULT nextval('month_cleaner_satisfaction_month_cleaner_satisfaction_id_seq'::regclass),
  "vendor_id" "pg_catalog"."int4",
  "clean_year" "pg_catalog"."int8",
  "clean_month" "pg_catalog"."int4",
  "count_of_cleans" "pg_catalog"."int4",
  "average_satisfaction_score" "pg_catalog"."numeric",
  CONSTRAINT "month_cleaner_satisfaction_pkey" PRIMARY KEY ("month_cleaner_satisfaction_id"),
  CONSTRAINT "fk_month_cleaner_satisfaction_vendor_id" FOREIGN KEY ("vendor_id") REFERENCES "public"."vendor" ("vendor_id") ON DELETE NO ACTION ON UPDATE NO ACTION
)
;

ALTER TABLE "public"."month_cleaner_satisfaction" 
  OWNER TO "postgres";
CREATE TABLE "public"."payment_made" (
  "payment_made_id" "pg_catalog"."int4" NOT NULL DEFAULT nextval('payment_made_payment_made_id_seq'::regclass),
  "vendor_id" "pg_catalog"."int4",
  "payment_date" "pg_catalog"."date",
  "vendor_invoice_id" "pg_catalog"."int4",
  "amount_paid" "pg_catalog"."numeric",
  "finance_emp_id" "pg_catalog"."int4",
  "approver_emp_id" "pg_catalog"."int4",
  CONSTRAINT "payment_made_pkey" PRIMARY KEY ("payment_made_id"),
  CONSTRAINT "fk_payment_made_approver" FOREIGN KEY ("approver_emp_id") REFERENCES "public"."employee" ("emp_id") ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT "fk_payment_made_employee" FOREIGN KEY ("finance_emp_id") REFERENCES "public"."employee" ("emp_id") ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT "fk_payment_made_vendor" FOREIGN KEY ("vendor_id") REFERENCES "public"."vendor" ("vendor_id") ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT "fk_payment_made_vendor_invoice" FOREIGN KEY ("vendor_invoice_id") REFERENCES "public"."vendor_invoice" ("vendor_invoice_id") ON DELETE NO ACTION ON UPDATE NO ACTION
)
;

ALTER TABLE "public"."payment_made" 
  OWNER TO "postgres";
CREATE TABLE "public"."payroll" (
  "payroll_id" "pg_catalog"."int4" NOT NULL DEFAULT nextval('payroll_payroll_id_seq'::regclass),
  "pay_date" "pg_catalog"."date",
  "total_net" "pg_catalog"."numeric",
  "total_tax" "pg_catalog"."numeric",
  "total_salaries" "pg_catalog"."numeric",
  "paid" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  CONSTRAINT "payroll_pkey" PRIMARY KEY ("payroll_id")
)
;

ALTER TABLE "public"."payroll" 
  OWNER TO "postgres";
CREATE TABLE "public"."payroll_detail" (
  "payroll_detail_id" "pg_catalog"."int4" NOT NULL DEFAULT nextval('payroll_detail_payroll_detail_id_seq'::regclass),
  "payroll_id" "pg_catalog"."int4",
  "pay_date" "pg_catalog"."date",
  "emp_id" "pg_catalog"."int4",
  "status" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "job_position_id" "pg_catalog"."int4",
  "standard_hours" "pg_catalog"."int4",
  "net_payment" "pg_catalog"."numeric",
  "taxation" "pg_catalog"."numeric",
  "total_payment" "pg_catalog"."numeric",
  "approver_emp_id" "pg_catalog"."int4",
  CONSTRAINT "payroll_detail_pkey" PRIMARY KEY ("payroll_detail_id"),
  CONSTRAINT "fk_payroll_detail_employees" FOREIGN KEY ("emp_id") REFERENCES "public"."employee" ("emp_id") ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT "fk_payroll_detail_job_position" FOREIGN KEY ("job_position_id") REFERENCES "public"."job_position" ("job_position_id") ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT "fk_payroll_detail_payroll" FOREIGN KEY ("payroll_id") REFERENCES "public"."payroll" ("payroll_id") ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT "fk_payroll_detail_status_lookup" FOREIGN KEY ("status") REFERENCES "public"."status_lookup" ("status_code") ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT "fk_payroll_detial_approver" FOREIGN KEY ("approver_emp_id") REFERENCES "public"."employee" ("emp_id") ON DELETE NO ACTION ON UPDATE NO ACTION
)
;

ALTER TABLE "public"."payroll_detail" 
  OWNER TO "postgres";
CREATE TABLE "public"."salaries" (
  "job_position_id" "pg_catalog"."int4" NOT NULL,
  "start_date" "pg_catalog"."date" NOT NULL,
  "annual_salary" "pg_catalog"."numeric",
  CONSTRAINT "salaries_pkey" PRIMARY KEY ("job_position_id", "start_date"),
  CONSTRAINT "fk_salaries_job_position" FOREIGN KEY ("job_position_id") REFERENCES "public"."job_position" ("job_position_id") ON DELETE NO ACTION ON UPDATE NO ACTION
)
;

ALTER TABLE "public"."salaries" 
  OWNER TO "postgres";
CREATE TABLE "public"."standard_hours_log" (
  "emp_id" "pg_catalog"."int4" NOT NULL,
  "start_date" "pg_catalog"."date" NOT NULL,
  "standard_hours" "pg_catalog"."int4",
  CONSTRAINT "standard_hours_log_pkey" PRIMARY KEY ("emp_id", "start_date"),
  CONSTRAINT "fk_standard_hours_log_employee" FOREIGN KEY ("emp_id") REFERENCES "public"."employee" ("emp_id") ON DELETE NO ACTION ON UPDATE NO ACTION
)
;

ALTER TABLE "public"."standard_hours_log" 
  OWNER TO "postgres";
CREATE TABLE "public"."status_lookup" (
  "status_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
  "description" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  CONSTRAINT "status_lookup_pkey" PRIMARY KEY ("status_code")
)
;

ALTER TABLE "public"."status_lookup" 
  OWNER TO "postgres";
CREATE TABLE "public"."tax_rates" (
  "gtr_id" "pg_catalog"."int4" NOT NULL DEFAULT nextval('tax_rates_gtr_id_seq'::regclass),
  "threshold" "pg_catalog"."numeric",
  "rate" "pg_catalog"."numeric",
  CONSTRAINT "tax_rates_pkey" PRIMARY KEY ("gtr_id")
)
;

ALTER TABLE "public"."tax_rates" 
  OWNER TO "postgres";
CREATE TABLE "public"."vendor" (
  "vendor_id" "pg_catalog"."int4" NOT NULL DEFAULT nextval('vendor_vendor_id_seq'::regclass),
  "vendor_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "street" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "suburb" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "city" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "state" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "post_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "vendor_type" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "notes" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "amount_owing" "pg_catalog"."numeric",
  CONSTRAINT "vendor_pkey" PRIMARY KEY ("vendor_id")
)
;

ALTER TABLE "public"."vendor" 
  OWNER TO "postgres";
CREATE TABLE "public"."vendor_invoice" (
  "vendor_invoice_id" "pg_catalog"."int4" NOT NULL DEFAULT nextval('vendor_invoice_vendor_invoice_id_seq'::regclass),
  "vendor_id" "pg_catalog"."int4",
  "invoice_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "invoice_date" "pg_catalog"."date",
  "amount" "pg_catalog"."numeric",
  "amount_owing" "pg_catalog"."numeric",
  "paid_flag" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
  "approver_emp_id" "pg_catalog"."int4",
  CONSTRAINT "vendor_invoice_pkey" PRIMARY KEY ("vendor_invoice_id"),
  CONSTRAINT "fk_vendor_invoice_approver" FOREIGN KEY ("approver_emp_id") REFERENCES "public"."employee" ("emp_id") ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT "fk_vendor_invoice_vendor" FOREIGN KEY ("vendor_id") REFERENCES "public"."vendor" ("vendor_id") ON DELETE NO ACTION ON UPDATE NO ACTION
)
;

ALTER TABLE "public"."vendor_invoice" 
  OWNER TO "postgres";

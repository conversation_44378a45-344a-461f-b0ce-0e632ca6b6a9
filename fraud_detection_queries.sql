-- 欺诈风险评估 - 真实数据查询

-- 1. 自我批准违规检测
SELECT 
    e.emp_id,
    e.first_name || ' ' || e.last_name AS employee_name,
    jp.position_title,
    COUNT(*) AS violation_count,
    SUM(gs.sale_amount) AS total_violation_amount,
    MIN(gs.sale_date) AS first_violation_date,
    MAX(gs.sale_date) AS last_violation_date
FROM glaze_sale gs
INNER JOIN employee e ON gs.emp_id = e.emp_id AND gs.referrer_emp_id = e.emp_id
INNER JOIN job_position jp ON e.job_position_id = jp.job_position_id
WHERE gs.sale_amount > 0
GROUP BY e.emp_id, e.first_name, e.last_name, jp.position_title
ORDER BY violation_count DESC, total_violation_amount DESC;

-- 2. 重复付款检测
WITH duplicate_payments AS (
    SELECT 
        sale_amount,
        sale_date,
        customer_id,
        COUNT(*) as duplicate_count,
        ARRAY_AGG(glaze_sale_id) as sale_ids
    FROM glaze_sale 
    WHERE sale_amount > 0
    GROUP BY sale_amount, sale_date, customer_id
    HAVING COUNT(*) > 1
)
SELECT 
    sale_amount,
    sale_date,
    customer_id,
    duplicate_count,
    sale_amount * (duplicate_count - 1) as potential_loss,
    sale_ids
FROM duplicate_payments
ORDER BY potential_loss DESC;

-- 3. 异常高额交易检测
WITH transaction_stats AS (
    SELECT 
        AVG(sale_amount) as mean_amount,
        STDDEV(sale_amount) as std_amount
    FROM glaze_sale 
    WHERE sale_amount > 0
),
z_scores AS (
    SELECT 
        gs.glaze_sale_id,
        gs.sale_amount,
        gs.sale_date,
        gs.customer_id,
        gs.emp_id,
        (gs.sale_amount - ts.mean_amount) / ts.std_amount as z_score
    FROM glaze_sale gs
    CROSS JOIN transaction_stats ts
    WHERE gs.sale_amount > 0
)
SELECT 
    glaze_sale_id,
    sale_amount,
    sale_date,
    customer_id,
    emp_id,
    ROUND(z_score::numeric, 2) as z_score
FROM z_scores
WHERE ABS(z_score) > 2.5
ORDER BY ABS(z_score) DESC;

-- 4. 员工ID数字模式分析
SELECT 
    emp_id,
    first_name || ' ' || last_name AS employee_name,
    emp_id % 13 as modulo_13,
    CASE WHEN emp_id % 13 = 0 THEN 'DIVISIBLE_BY_13' ELSE 'NOT_DIVISIBLE' END as pattern_flag
FROM employee
WHERE end_date IS NULL
ORDER BY emp_id;

-- 5. 统计可被13整除的员工ID
SELECT 
    COUNT(*) as total_employees,
    COUNT(CASE WHEN emp_id % 13 = 0 THEN 1 END) as divisible_by_13,
    ROUND(COUNT(CASE WHEN emp_id % 13 = 0 THEN 1 END)::numeric / COUNT(*) * 100, 1) as percentage
FROM employee
WHERE end_date IS NULL;

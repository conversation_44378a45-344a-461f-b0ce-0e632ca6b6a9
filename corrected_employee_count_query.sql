-- 修正版：按职位统计在职员工数量
-- 问题：原查询因为JOIN导致重复计算员工数量

-- 方法1：使用DISTINCT COUNT避免重复
SELECT
    ROW_NUMBER() OVER(ORDER BY COUNT(DISTINCT e.emp_id) DESC) AS rank,
    jp.position_title,
    COUNT(DISTINCT e.emp_id) AS active_employees,
    AVG(pd.total_payment) AS avg_salary,
    COUNT(DISTINCT gs.glaze_sale_id) AS total_transactions,
    SUM(gs.sale_amount) AS total_revenue
FROM job_position jp
INNER JOIN employee e ON jp.job_position_id = e.job_position_id
LEFT JOIN payroll_detail pd ON e.emp_id = pd.emp_id
LEFT JOIN glaze_sale gs ON e.emp_id = gs.emp_id
WHERE e.end_date IS NULL
GROUP BY jp.position_title
ORDER BY active_employees DESC;

-- 方法2：分别计算各项指标（推荐）
WITH employee_counts AS (
    SELECT 
        jp.position_title,
        COUNT(*) AS active_employees
    FROM job_position jp
    INNER JOIN employee e ON jp.job_position_id = e.job_position_id
    WHERE e.end_date IS NULL
    GROUP BY jp.position_title
),
salary_stats AS (
    SELECT 
        jp.position_title,
        AVG(pd.total_payment) AS avg_salary
    FROM job_position jp
    INNER JOIN employee e ON jp.job_position_id = e.job_position_id
    LEFT JOIN payroll_detail pd ON e.emp_id = pd.emp_id
    WHERE e.end_date IS NULL
    GROUP BY jp.position_title
),
transaction_stats AS (
    SELECT 
        jp.position_title,
        COUNT(gs.glaze_sale_id) AS total_transactions,
        SUM(gs.sale_amount) AS total_revenue
    FROM job_position jp
    INNER JOIN employee e ON jp.job_position_id = e.job_position_id
    LEFT JOIN glaze_sale gs ON e.emp_id = gs.emp_id
    WHERE e.end_date IS NULL
    GROUP BY jp.position_title
)
SELECT
    ROW_NUMBER() OVER(ORDER BY ec.active_employees DESC) AS rank,
    ec.position_title,
    ec.active_employees,
    ROUND(ss.avg_salary, 2) AS avg_salary,
    ts.total_transactions,
    ts.total_revenue
FROM employee_counts ec
LEFT JOIN salary_stats ss ON ec.position_title = ss.position_title
LEFT JOIN transaction_stats ts ON ec.position_title = ts.position_title
ORDER BY ec.active_employees DESC;

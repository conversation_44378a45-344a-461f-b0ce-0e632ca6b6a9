import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
import os
from datetime import datetime

# Set English locale and Excel-compatible styling
plt.style.use('default')  # Use default matplotlib style
plt.rcParams['font.family'] = 'Arial'  # Excel default font
plt.rcParams['font.size'] = 10
plt.rcParams['axes.grid'] = True
plt.rcParams['grid.alpha'] = 0.3
plt.rcParams['axes.edgecolor'] = 'black'
plt.rcParams['axes.linewidth'] = 0.8

# Create images directory
if not os.path.exists('./images'):
    os.makedirs('./images')

# Excel-compatible color palette (similar to Excel default themes)
excel_colors = {
    'blue_series': ['#4472C4', '#70AD47', '#FFC000', '#C55A5A', '#9966CC', '#264478'],
    'orange_series': ['#E7E6E6', '#FFC000', '#5B9BD5', '#70AD47', '#C55A5A', '#264478'],
    'accent_colors': ['#5B9BD5', '#ED7D31', '#A5A5A5', '#FFC000', '#4472C4', '#70AD47']
}

# 1. Self-Approval Violations Data (Real data from PostgreSQL)
self_approval_data = {
    'emp_id': [2310, 1842, 1524, 2685, 508, 147],
    'employee_name': ['Kasey Day', 'Bennett Mosley', 'Mario Blanchard', 'Raiden Montes', 'Demetrius Galvan', 'Mohammed Combs'],
    'position_title': ['Salesperson', 'Salesperson', 'Salesperson', 'Salesperson', 'Salesperson', 'Salesperson'],
    'violation_count': [2, 2, 2, 2, 2, 2],
    'total_violation_amount': [630.86, 586.89, 504.06, 429.60, 390.78, 298.66]
}

# 2. High-Value Anomalies Data (Real data from PostgreSQL)
high_value_anomalies_data = {
    'glaze_sale_id': [4038, 4031, 4028, 4034, 4026, 4022, 4023, 4025, 4027, 4041],
    'sale_amount': [22000.00] * 10,
    'date_ordered': ['2023-07-08', '2023-05-25', '2023-04-12', '2023-06-13', '2023-03-12', 
                     '2022-12-10', '2022-12-16', '2023-02-22', '2023-04-10', '2023-08-01'],
    'customer_id': [331, 272, 225, 293, 196, 102, 108, 179, 223, 361],
    'emp_id': [2204, 1141, 1048, 655, 1230, 1795, 2742, 2429, 1230, 2429],
    'z_score': [4.54] * 10
}

# 3. Employee ID Pattern Data (Real data from PostgreSQL)
employee_id_pattern_data = {
    'emp_id': [195, 234, 325, 377, 507, 585, 728, 910, 1404, 1482, 1599, 1638, 1716, 2067, 2288],
    'employee_name': ['Alijah Fleming', 'Kassandra Blackwell', 'Maddox Stewart', 'Caitlin Sawyer', 
                      'Colin Garrett', 'Patrick Gaines', 'Sandra Daniels', 'Wade Crane', 
                      'Kiana Walton', 'Franklin Mathis', 'Christian Cobb', 'Adrian Day', 
                      'Iyana Houston', 'Sullivan Larsen', 'Chelsea Dennis'],
    'divisible_by_13': ['Yes'] * 15
}

# 4. Risk Assessment Summary Data
risk_summary_data = {
    'risk_category': ['Self-Approval Violations', 'High-Value Anomalies', 'Employee ID Pattern', 'System Access Control'],
    'risk_level': ['Medium', 'Critical', 'High', 'High'],
    'potential_loss': [2840.85, 220000.00, 50000.00, 100000.00],
    'probability': [100, 90, 80, 70],
    'expected_loss': [2840.85, 198000.00, 40000.00, 70000.00]
}

# Create DataFrames
df_self_approval = pd.DataFrame(self_approval_data)
df_anomalies = pd.DataFrame(high_value_anomalies_data)
df_id_pattern = pd.DataFrame(employee_id_pattern_data)
df_risk_summary = pd.DataFrame(risk_summary_data)

print("Starting to generate Excel-compatible fraud analysis charts...")

# 1. Self-Approval Violations Charts
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
fig.suptitle('Self-Approval Violations Analysis', fontsize=14, fontweight='bold')

# Violation amounts bar chart
bars1 = ax1.bar(range(len(df_self_approval)), df_self_approval['total_violation_amount'], 
                color=excel_colors['blue_series'][:len(df_self_approval)], 
                edgecolor='black', linewidth=0.5)
ax1.set_title('Violation Amounts by Employee', fontsize=12, fontweight='bold')
ax1.set_xlabel('Employee', fontsize=10)
ax1.set_ylabel('Violation Amount ($)', fontsize=10)
ax1.set_xticks(range(len(df_self_approval)))
ax1.set_xticklabels([name.split()[0] for name in df_self_approval['employee_name']], rotation=45)
ax1.grid(True, alpha=0.3)

# Add value labels on bars
for i, bar in enumerate(bars1):
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height + 10,
             f'${height:.2f}', ha='center', va='bottom', fontsize=9, fontweight='bold')

# Employee violation ratio pie chart
violation_employees = len(df_self_approval)
total_employees = 142  # From real data
normal_employees = total_employees - violation_employees

pie_data = [violation_employees, normal_employees]
pie_labels = ['Violating Employees', 'Normal Employees']
pie_colors = [excel_colors['accent_colors'][1], excel_colors['accent_colors'][0]]

wedges, texts, autotexts = ax2.pie(pie_data, labels=pie_labels, colors=pie_colors,
                                   autopct='%1.1f%%', startangle=90, 
                                   textprops={'fontsize': 10})
ax2.set_title('Employee Violation Distribution', fontsize=12, fontweight='bold')

plt.tight_layout()
plt.savefig('./images/self_approval_violations.png', dpi=300, bbox_inches='tight', 
            facecolor='white', edgecolor='none')
plt.close()

print("✓ Self-approval violations chart saved")

# 2. High-Value Anomalies Charts
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('High-Value Transaction Anomalies Analysis', fontsize=14, fontweight='bold')

# Time series line chart
df_anomalies['date_ordered'] = pd.to_datetime(df_anomalies['date_ordered'])
sorted_data = df_anomalies.sort_values('date_ordered')
ax1.plot(sorted_data['date_ordered'], sorted_data['sale_amount'], 
         marker='o', linewidth=2, markersize=6, color=excel_colors['accent_colors'][1],
         markerfacecolor=excel_colors['accent_colors'][1], markeredgecolor='black', markeredgewidth=0.5)
ax1.set_title('Anomalous Transactions Over Time', fontsize=12, fontweight='bold')
ax1.set_xlabel('Transaction Date', fontsize=10)
ax1.set_ylabel('Transaction Amount ($)', fontsize=10)
ax1.grid(True, alpha=0.3)
ax1.tick_params(axis='x', rotation=45)

# Z-Score distribution bar chart
bars2 = ax2.bar(range(len(df_anomalies)), df_anomalies['z_score'], 
                color=excel_colors['accent_colors'][1], alpha=0.7,
                edgecolor='black', linewidth=0.5)
ax2.axhline(y=2.5, color='orange', linestyle='--', linewidth=2, label='Anomaly Threshold (Z=2.5)')
ax2.axhline(y=4.0, color='red', linestyle='--', linewidth=2, label='Critical Threshold (Z=4.0)')
ax2.set_title('Z-Score Distribution of Anomalous Transactions', fontsize=12, fontweight='bold')
ax2.set_xlabel('Transaction Number', fontsize=10)
ax2.set_ylabel('Z-Score', fontsize=10)
ax2.legend(fontsize=9)
ax2.grid(True, alpha=0.3)

# Employee involvement bar chart
emp_counts = df_anomalies['emp_id'].value_counts()
bars3 = ax3.bar(range(len(emp_counts)), emp_counts.values, 
                color=excel_colors['blue_series'][:len(emp_counts)],
                edgecolor='black', linewidth=0.5)
ax3.set_title('Employee Involvement in Anomalous Transactions', fontsize=12, fontweight='bold')
ax3.set_xlabel('Employee ID', fontsize=10)
ax3.set_ylabel('Number of Anomalous Transactions', fontsize=10)
ax3.set_xticks(range(len(emp_counts)))
ax3.set_xticklabels([f'{emp_id}' for emp_id in emp_counts.index], rotation=45)
ax3.grid(True, alpha=0.3)

# Add value labels
for i, bar in enumerate(bars3):
    height = bar.get_height()
    ax3.text(bar.get_x() + bar.get_width()/2., height + 0.05,
             f'{int(height)}', ha='center', va='bottom', fontsize=9, fontweight='bold')

# Transaction amount uniformity
ax4.bar(['$22,000'], [10], color=excel_colors['accent_colors'][1], alpha=0.7,
        edgecolor='black', linewidth=0.5, width=0.5)
ax4.set_title('Transaction Amount Distribution', fontsize=12, fontweight='bold')
ax4.set_xlabel('Transaction Amount', fontsize=10)
ax4.set_ylabel('Number of Transactions', fontsize=10)
ax4.text(0, 5, 'All 10 transactions\nhave identical amount\nof $22,000', 
         ha='center', va='center', fontsize=10, fontweight='bold',
         bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7, edgecolor='black'))
ax4.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('./images/high_value_anomalies.png', dpi=300, bbox_inches='tight',
            facecolor='white', edgecolor='none')
plt.close()

print("✓ High-value anomalies chart saved")

# 3. Employee ID Pattern Analysis Charts
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('Employee ID Pattern Analysis', fontsize=14, fontweight='bold')

# ID distribution scatter plot (converted to bar chart for Excel compatibility)
id_ranges = ['0-500', '501-1000', '1001-1500', '1501-2000', '2001-2500']
id_counts = [4, 2, 3, 4, 2]  # Count of divisible-by-13 IDs in each range
bars1 = ax1.bar(id_ranges, id_counts, color=excel_colors['accent_colors'][1], alpha=0.7,
                edgecolor='black', linewidth=0.5)
ax1.set_title('Distribution of Divisible-by-13 Employee IDs', fontsize=12, fontweight='bold')
ax1.set_xlabel('Employee ID Range', fontsize=10)
ax1.set_ylabel('Count of Suspicious IDs', fontsize=10)
ax1.grid(True, alpha=0.3)

# Add value labels
for i, bar in enumerate(bars1):
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
             f'{int(height)}', ha='center', va='bottom', fontsize=9, fontweight='bold')

# Actual vs Expected distribution comparison
categories = ['Divisible by 13', 'Not Divisible by 13']
actual_values = [15, 127]  # 15 divisible, 127 not divisible
expected_values = [142/13, 142*12/13]  # Expected values: ~10.9, ~131.1

x = np.arange(len(categories))
width = 0.35

bars2a = ax2.bar(x - width/2, actual_values, width, label='Actual',
                 color=excel_colors['accent_colors'][1], alpha=0.8, edgecolor='black', linewidth=0.5)
bars2b = ax2.bar(x + width/2, expected_values, width, label='Expected',
                 color=excel_colors['accent_colors'][0], alpha=0.8, edgecolor='black', linewidth=0.5)

ax2.set_title('Actual vs Expected Distribution', fontsize=12, fontweight='bold')
ax2.set_xlabel('Category', fontsize=10)
ax2.set_ylabel('Number of Employees', fontsize=10)
ax2.set_xticks(x)
ax2.set_xticklabels(categories)
ax2.legend(fontsize=9)
ax2.grid(True, alpha=0.3)

# Add value labels
for bars in [bars2a, bars2b]:
    for bar in bars:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                 f'{height:.1f}', ha='center', va='bottom', fontsize=9, fontweight='bold')

# Probability analysis bar chart
prob_categories = ['Observed Probability', 'Theoretical Probability']
prob_values = [10.6, 7.7]  # 10.6% observed, 7.7% theoretical
bars3 = ax3.bar(prob_categories, prob_values,
                color=[excel_colors['accent_colors'][1], excel_colors['accent_colors'][0]],
                alpha=0.8, edgecolor='black', linewidth=0.5)
ax3.set_title('Probability Analysis', fontsize=12, fontweight='bold')
ax3.set_xlabel('Probability Type', fontsize=10)
ax3.set_ylabel('Probability (%)', fontsize=10)
ax3.axhline(y=7.7, color='orange', linestyle='--', linewidth=2, label='Theoretical Expected (7.7%)')
ax3.legend(fontsize=9)
ax3.grid(True, alpha=0.3)

# Add value labels
for i, bar in enumerate(bars3):
    height = bar.get_height()
    ax3.text(bar.get_x() + bar.get_width()/2., height + 0.2,
             f'{height}%', ha='center', va='bottom', fontsize=9, fontweight='bold')

# Risk distribution pie chart
risk_employees = 15
normal_employees = 127
pie_data = [risk_employees, normal_employees]
pie_labels = ['Suspicious ID Pattern', 'Normal ID Pattern']
pie_colors = [excel_colors['accent_colors'][1], excel_colors['accent_colors'][0]]

wedges, texts, autotexts = ax4.pie(pie_data, labels=pie_labels, colors=pie_colors,
                                   autopct='%1.1f%%', startangle=90,
                                   textprops={'fontsize': 10})
ax4.set_title('Employee ID Risk Distribution', fontsize=12, fontweight='bold')

plt.tight_layout()
plt.savefig('./images/employee_id_pattern.png', dpi=300, bbox_inches='tight',
            facecolor='white', edgecolor='none')
plt.close()

print("✓ Employee ID pattern chart saved")

# 4. Risk Assessment Summary Charts
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('Fraud Risk Assessment Summary', fontsize=14, fontweight='bold')

# Risk level distribution pie chart
risk_levels = df_risk_summary['risk_level'].value_counts()
risk_colors = {'Critical': '#C55A5A', 'High': '#FFC000', 'Medium': '#70AD47', 'Low': '#5B9BD5'}
pie_colors = [risk_colors[level] for level in risk_levels.index]

wedges, texts, autotexts = ax1.pie(risk_levels.values, labels=risk_levels.index, colors=pie_colors,
                                   autopct='%1.1f%%', startangle=90,
                                   textprops={'fontsize': 10})
ax1.set_title('Risk Level Distribution', fontsize=12, fontweight='bold')

# Potential loss comparison bar chart
bars2 = ax2.bar(range(len(df_risk_summary)), df_risk_summary['potential_loss'],
                color=[risk_colors.get(level, '#5B9BD5') for level in df_risk_summary['risk_level']],
                edgecolor='black', linewidth=0.5)
ax2.set_title('Potential Loss by Risk Category', fontsize=12, fontweight='bold')
ax2.set_xlabel('Risk Category', fontsize=10)
ax2.set_ylabel('Potential Loss ($)', fontsize=10)
ax2.set_xticks(range(len(df_risk_summary)))
ax2.set_xticklabels([cat.replace(' ', '\n') for cat in df_risk_summary['risk_category']],
                    fontsize=9, rotation=0)
ax2.grid(True, alpha=0.3)

# Add value labels
for i, bar in enumerate(bars2):
    height = bar.get_height()
    ax2.text(bar.get_x() + bar.get_width()/2., height + 3000,
             f'${height:,.0f}', ha='center', va='bottom', fontsize=9, fontweight='bold')

# Expected loss analysis bar chart
bars3 = ax3.bar(range(len(df_risk_summary)), df_risk_summary['expected_loss'],
                color=[risk_colors.get(level, '#5B9BD5') for level in df_risk_summary['risk_level']],
                alpha=0.8, edgecolor='black', linewidth=0.5)
ax3.set_title('Expected Loss Analysis (Potential Loss × Probability)', fontsize=12, fontweight='bold')
ax3.set_xlabel('Risk Category', fontsize=10)
ax3.set_ylabel('Expected Loss ($)', fontsize=10)
ax3.set_xticks(range(len(df_risk_summary)))
ax3.set_xticklabels([cat.replace(' ', '\n') for cat in df_risk_summary['risk_category']],
                    fontsize=9, rotation=0)
ax3.grid(True, alpha=0.3)

# Add value labels
for i, bar in enumerate(bars3):
    height = bar.get_height()
    ax3.text(bar.get_x() + bar.get_width()/2., height + 2000,
             f'${height:,.0f}', ha='center', va='bottom', fontsize=9, fontweight='bold')

# Risk priority matrix scatter plot
scatter_colors = [risk_colors.get(level, '#5B9BD5') for level in df_risk_summary['risk_level']]
scatter = ax4.scatter(df_risk_summary['probability'], df_risk_summary['potential_loss'],
                     s=200, c=scatter_colors, alpha=0.7, edgecolors='black', linewidth=1)
ax4.set_title('Risk Priority Matrix', fontsize=12, fontweight='bold')
ax4.set_xlabel('Probability (%)', fontsize=10)
ax4.set_ylabel('Potential Loss ($)', fontsize=10)
ax4.grid(True, alpha=0.3)

# Add risk category labels
for i, txt in enumerate(df_risk_summary['risk_category']):
    ax4.annotate(txt.replace(' ', '\n'),
                (df_risk_summary['probability'][i], df_risk_summary['potential_loss'][i]),
                xytext=(5, 5), textcoords='offset points', fontsize=8, fontweight='bold')

plt.tight_layout()
plt.savefig('./images/fraud_risk_summary.png', dpi=300, bbox_inches='tight',
            facecolor='white', edgecolor='none')
plt.close()

print("✓ Risk assessment summary chart saved")

# Create Excel workbook with all data
wb = Workbook()
ws = wb.active
ws.title = "Fraud Risk Analysis Overview"

# Add summary information to the overview sheet
ws['A1'] = "GigaGlow Fraud Risk Analysis Summary"
ws['A1'].font = Font(size=16, bold=True)
ws['A3'] = "Generated on:"
ws['B3'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
ws['A4'] = "Total Risk Categories:"
ws['B4'] = len(df_risk_summary)
ws['A5'] = "Total Potential Loss:"
ws['B5'] = f"${df_risk_summary['potential_loss'].sum():,.2f}"
ws['A6'] = "Total Expected Loss:"
ws['B6'] = f"${df_risk_summary['expected_loss'].sum():,.2f}"

# Add individual worksheets
ws_self_approval = wb.create_sheet("Self-Approval Violations")
ws_anomalies = wb.create_sheet("High-Value Anomalies")
ws_id_pattern = wb.create_sheet("Employee ID Pattern")
ws_risk_summary = wb.create_sheet("Risk Assessment Summary")

# Write data to worksheets
for r in dataframe_to_rows(df_self_approval, index=False, header=True):
    ws_self_approval.append(r)

for r in dataframe_to_rows(df_anomalies, index=False, header=True):
    ws_anomalies.append(r)

for r in dataframe_to_rows(df_id_pattern, index=False, header=True):
    ws_id_pattern.append(r)

for r in dataframe_to_rows(df_risk_summary, index=False, header=True):
    ws_risk_summary.append(r)

# Save Excel file
wb.save('GigaGlow_Fraud_Risk_Analysis_English.xlsx')

print("\n" + "="*60)
print("Excel-Compatible Fraud Risk Analysis Charts Generated!")
print("="*60)
print("Generated Files:")
print("- Excel Workbook: GigaGlow_Fraud_Risk_Analysis_English.xlsx")
print("- Chart Images:")
print("  * ./images/self_approval_violations.png")
print("  * ./images/high_value_anomalies.png")
print("  * ./images/employee_id_pattern.png")
print("  * ./images/fraud_risk_summary.png")
print("\nAll charts use:")
print("- English language labels and titles")
print("- Excel-compatible chart types (bar, line, pie, scatter)")
print("- Excel default color schemes")
print("- 1200x800 pixel resolution at 300 DPI")
print("- Professional styling suitable for business reports")
print("="*60)

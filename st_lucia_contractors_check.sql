-- Question 2: St Lucia roof cleaning contractors analysis
SELECT
    COUNT(DISTINCT v.vendor_id) as local_contractors,
    COUNT(DISTINCT CASE WHEN gs.glaze_sale_id IS NOT NULL THEN v.vendor_id END) as active_contractors,
    COUNT(gs.glaze_sale_id) as total_services,
    SUM(gs.sale_amount) as total_revenue
FROM vendor v
LEFT JOIN glaze_sale gs ON v.vendor_id = gs.vendor_id
    AND gs.sale_type = 'CLEANING-FEE'
LEFT JOIN customer c ON gs.customer_id = c.customer_id
    AND c.suburb = 'ST LUCIA'
WHERE v.vendor_type = 'CC'
    AND (v.suburb = 'ST LUCIA' OR c.suburb = 'ST LUCIA');
